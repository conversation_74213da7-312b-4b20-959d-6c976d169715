# 自演系统平台 - 环境变量配置示例

# 数据库配置
DATABASE_URL=sqlite:///instance/ai_dev_dashboard.db

# Flask应用配置
SECRET_KEY=your_secret_key_here_please_change_in_production
FLASK_ENV=development
FLASK_DEBUG=True

# 服务端口配置
FLASK_PORT=5002
VITE_PORT=5175

# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ACCESS_TOKEN_EXPIRES=3600

# CORS配置
CORS_ORIGINS=http://localhost:5175,http://localhost:3000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 职业模板配置
DEFAULT_PROFESSION=ai_engineer
ENA<PERSON>E_PROFESSION_SWITCHING=True

# 功能开关
ENABLE_REGISTRATION=True
ENABLE_PASSWORD_RESET=False
ENABLE_EMAIL_VERIFICATION=False

# 性能配置
MAX_CONTENT_LENGTH=16777216  # 16MB
DATABASE_POOL_SIZE=10
DATABASE_POOL_TIMEOUT=30

# 安全配置
SESSION_COOKIE_SECURE=False  # 生产环境设置为True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# API配置
API_RATE_LIMIT=100  # 每分钟请求数
API_TIMEOUT=30      # 秒

# 开发配置
ENABLE_DEBUG_TOOLBAR=False
ENABLE_PROFILER=False
