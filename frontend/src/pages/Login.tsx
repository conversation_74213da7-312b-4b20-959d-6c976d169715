import { useState } from 'react';
import { authAPI } from '../api';

const Login = ({ onLoginSuccess }: { onLoginSuccess: (redirectPath?: string) => void }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    console.log('开始登录，用户名:', username);
    
    try {
      const res = await authAPI.login({ username, password });
      console.log('登录成功:', res.data);
      
      localStorage.setItem('token', res.data.token);
      localStorage.setItem('user', JSON.stringify({ username: res.data.username, user_id: res.data.user_id }));
      
      // 跳转到原目标页面或首页
      const params = new URLSearchParams(window.location.search);
      const redirect = params.get('redirect');
      
      console.log('准备跳转到:', redirect || '/');
      onLoginSuccess(redirect || '/');
    } catch (err: any) {
      console.error('登录失败:', err);
      const errorMessage = err?.response?.data?.message || '登录失败，请检查用户名和密码';
      setError(errorMessage);
      console.log('设置错误信息:', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
      <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-96">
        <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">登录</h2>
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-center">
            <strong>登录失败：</strong>{error}
          </div>
        )}
        <div className="mb-4">
          <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">用户名</label>
          <input 
            type="text" 
            value={username} 
            onChange={e => setUsername(e.target.value)} 
            required 
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入用户名"
          />
        </div>
        <div className="mb-6">
          <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">密码</label>
          <input 
            type="password" 
            value={password} 
            onChange={e => setPassword(e.target.value)} 
            required 
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入密码"
          />
        </div>
        <button 
          type="submit" 
          disabled={loading} 
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 rounded-lg font-semibold transition-colors"
        >
          {loading ? '登录中...' : '登录'}
        </button>
        <p className="mt-4 text-center text-gray-600 dark:text-gray-400">
          <button
            onClick={() => {
              window.history.pushState({}, '', '/register');
              window.dispatchEvent(new Event('popstate'));
            }}
            className="text-blue-600 hover:text-blue-700 hover:underline"
          >
            注册
          </button>
          <span className="mx-4">|</span>
          <button
            onClick={() => {
              window.history.pushState({}, '', '/forgot-password');
              window.dispatchEvent(new Event('popstate'));
            }}
            className="text-blue-600 hover:text-blue-700 hover:underline"
          >
            忘记密码
          </button>
        </p>
        
      </form>
    </div>
  );
};

export default Login;