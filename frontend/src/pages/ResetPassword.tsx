import React, { useState, useEffect } from 'react';
import { authAPI } from '../api';

const ResetPassword: React.FC = () => {
    const [email, setEmail] = useState('');
    const [token, setToken] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [step, setStep] = useState(1); // 1: 发送邮件, 2: 重置密码
    const [loading, setLoading] = useState(false);

    // 检查URL中是否有token参数
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const urlToken = urlParams.get('token');
        if (urlToken) {
            setToken(urlToken);
            setStep(2);
        }
    }, []);

    // 发送重置邮件
    const handleSendEmail = async () => {
        if (!email) {
            setError('请输入邮箱地址');
            return;
        }

        setLoading(true);
        setError('');
        setMessage('');

        try {
            await authAPI.forgotPassword(email);
            setMessage('重置邮件已发送，请检查您的邮箱');
            setStep(2);
        } catch (err: any) {
            setError(err?.response?.data?.message || '发送邮件失败');
        } finally {
            setLoading(false);
        }
    };

    // 重置密码
    const handleResetPassword = async () => {
        if (!token) {
            setError('请输入重置令牌');
            return;
        }
        
        if (!newPassword) {
            setError('请输入新密码');
            return;
        }

        if (newPassword.length < 6) {
            setError('密码长度至少6位');
            return;
        }

        if (newPassword !== confirmPassword) {
            setError('两次输入的密码不一致');
            return;
        }

        setLoading(true);
        setError('');
        setMessage('');

        try {
            await authAPI.resetPassword(token, newPassword);
            setMessage('密码重置成功！正在跳转到登录页面...');
            setTimeout(() => {
                window.history.pushState({}, '', '/login');
                window.dispatchEvent(new Event('popstate'));
            }, 2000);
        } catch (err: any) {
            setError(err?.response?.data?.message || '重置失败');
        } finally {
            setLoading(false);
        }
    };

    const handleBackToLogin = () => {
        window.history.pushState({}, '', '/login');
        window.dispatchEvent(new Event('popstate'));
    };

    return (
        <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
            <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md w-96">
                <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-white">
                    {step === 1 ? '忘记密码' : '重置密码'}
                </h2>

                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}

                {message && (
                    <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                        {message}
                    </div>
                )}

                {step === 1 ? (
                    // 第一步：发送重置邮件
                    <div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                            请输入您的注册邮箱，我们将向您发送密码重置链接。
                        </p>
                        
                        <div className="mb-6">
                            <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                邮箱地址
                            </label>
                            <input
                                type="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="请输入注册邮箱"
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        <button
                            onClick={handleSendEmail}
                            disabled={loading}
                            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 rounded-lg font-semibold transition-colors mb-4"
                        >
                            {loading ? '发送中...' : '发送重置邮件'}
                        </button>

                        <div className="text-center">
                            <button
                                onClick={() => setStep(2)}
                                className="text-blue-600 hover:text-blue-700 hover:underline text-sm"
                            >
                                已有重置令牌？直接重置密码
                            </button>
                        </div>
                    </div>
                ) : (
                    // 第二步：重置密码
                    <div>
                        <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                            请输入邮件中的重置令牌和新密码。
                        </p>

                        <div className="mb-4">
                            <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                重置令牌
                            </label>
                            <input
                                type="text"
                                value={token}
                                onChange={(e) => setToken(e.target.value)}
                                placeholder="请输入邮件中的重置令牌"
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        <div className="mb-4">
                            <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                新密码
                            </label>
                            <input
                                type="password"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                placeholder="请输入新密码（至少6位）"
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        <div className="mb-6">
                            <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                                确认新密码
                            </label>
                            <input
                                type="password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                placeholder="请再次输入新密码"
                                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        <button
                            onClick={handleResetPassword}
                            disabled={loading}
                            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 rounded-lg font-semibold transition-colors mb-4"
                        >
                            {loading ? '重置中...' : '重置密码'}
                        </button>

                        <div className="text-center">
                            <button
                                onClick={() => setStep(1)}
                                className="text-blue-600 hover:text-blue-700 hover:underline text-sm"
                            >
                                返回发送邮件
                            </button>
                        </div>
                    </div>
                )}

                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <p className="text-center text-gray-600 dark:text-gray-400">
                        <button
                            onClick={handleBackToLogin}
                            className="text-blue-600 hover:text-blue-700 hover:underline"
                        >
                            返回登录
                        </button>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;