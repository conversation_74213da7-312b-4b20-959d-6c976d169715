import { useState, useEffect } from 'react';
import MainLayout from '../components/MainLayout';
import { assessmentAPI } from '../api';
import { Plus, Edit, Trash2, Save, X, Clock, BookOpen, Link, CheckCircle } from 'lucide-react';

interface LearningActivity {
  id?: number;
  activity_type: string;
  title: string;
  hours_spent: number;
  knowledge_repo_link: string;
  completion_status: string;
  notes: string;
  date: string;
}

const LearningPage = () => {
  const [activities, setActivities] = useState<LearningActivity[]>([]);
  const [isAddingActivity, setIsAddingActivity] = useState(false);
  const [editingActivity, setEditingActivity] = useState<number | null>(null);
  const [newActivity, setNewActivity] = useState<LearningActivity>({
    activity_type: '课程',
    title: '',
    hours_spent: 0,
    knowledge_repo_link: '',
    completion_status: '进行中',
    notes: '',
    date: new Date().toISOString().split('T')[0]
  });

  const activityTypes = ['课程', '书籍', '论文', '视频', '实践项目', '会议/讲座'];
  const statusOptions = ['计划中', '进行中', '已完成', '暂停'];

  useEffect(() => {
    loadActivities();
  }, []);

  const loadActivities = async () => {
    try {
      const response = await assessmentAPI.getLearningActivities();
      setActivities(response.data);
    } catch (error) {
      console.error('加载学习活动失败:', error);
    }
  };

  const handleAddActivity = async () => {
    try {
      await assessmentAPI.addLearningActivity(newActivity);
      setIsAddingActivity(false);
      setNewActivity({
        activity_type: '课程',
        title: '',
        hours_spent: 0,
        knowledge_repo_link: '',
        completion_status: '进行中',
        notes: '',
        date: new Date().toISOString().split('T')[0]
      });
      loadActivities();
    } catch (error) {
      console.error('添加学习活动失败:', error);
    }
  };

  const handleUpdateActivity = async (activityId: number, updatedActivity: LearningActivity) => {
    try {
      await assessmentAPI.updateLearningActivity(activityId, updatedActivity);
      setEditingActivity(null);
      loadActivities();
    } catch (error) {
      console.error('更新学习活动失败:', error);
    }
  };

  const handleDeleteActivity = async (activityId: number) => {
    if (window.confirm('确定要删除这个学习活动吗？')) {
      try {
        await assessmentAPI.deleteLearningActivity(activityId);
        loadActivities();
      } catch (error) {
        console.error('删除学习活动失败:', error);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成': return 'bg-green-100 text-green-800';
      case '进行中': return 'bg-blue-100 text-blue-800';
      case '计划中': return 'bg-yellow-100 text-yellow-800';
      case '暂停': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case '课程': return <BookOpen className="h-4 w-4" />;
      case '书籍': return <BookOpen className="h-4 w-4" />;
      case '论文': return <BookOpen className="h-4 w-4" />;
      case '视频': return <BookOpen className="h-4 w-4" />;
      case '实践项目': return <CheckCircle className="h-4 w-4" />;
      case '会议/讲座': return <BookOpen className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const totalHours = activities.reduce((sum, activity) => sum + (activity.hours_spent || 0), 0);
  const completedActivities = activities.filter(activity => activity.completion_status === '已完成').length;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">学习能力诊断</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              追踪学习时间，管理知识库，提升学习效率
            </p>
          </div>
          <button
            onClick={() => setIsAddingActivity(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加学习活动
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总学习时间</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalHours}小时</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">学习活动</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{activities.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已完成</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{completedActivities}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 添加学习活动表单 */}
        {isAddingActivity && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加学习活动</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  活动类型
                </label>
                <select
                  value={newActivity.activity_type}
                  onChange={(e) => setNewActivity({...newActivity, activity_type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {activityTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标题
                </label>
                <input
                  type="text"
                  value={newActivity.title}
                  onChange={(e) => setNewActivity({...newActivity, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="学习内容标题"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  投入时间（小时）
                </label>
                <input
                  type="number"
                  value={newActivity.hours_spent}
                  onChange={(e) => setNewActivity({...newActivity, hours_spent: parseFloat(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="0"
                  step="0.5"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  完成状态
                </label>
                <select
                  value={newActivity.completion_status}
                  onChange={(e) => setNewActivity({...newActivity, completion_status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {statusOptions.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  知识库链接
                </label>
                <input
                  type="url"
                  value={newActivity.knowledge_repo_link}
                  onChange={(e) => setNewActivity({...newActivity, knowledge_repo_link: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://..."
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  学习笔记
                </label>
                <textarea
                  value={newActivity.notes}
                  onChange={(e) => setNewActivity({...newActivity, notes: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="记录学习心得、重点内容等..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingActivity(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddActivity}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 学习活动列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">学习活动记录</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {activities.map((activity) => (
              <div key={activity.id} className="p-6">
                {editingActivity === activity.id ? (
                  // 编辑表单
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          活动类型
                        </label>
                        <select
                          value={activity.activity_type}
                          onChange={(e) => {
                            const updatedActivity = { ...activity, activity_type: e.target.value };
                            setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {activityTypes.map(type => (
                            <option key={type} value={type}>{type}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          标题
                        </label>
                        <input
                          type="text"
                          value={activity.title}
                          onChange={(e) => {
                            const updatedActivity = { ...activity, title: e.target.value };
                            setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          投入时间（小时）
                        </label>
                        <input
                          type="number"
                          value={activity.hours_spent}
                          onChange={(e) => {
                            const updatedActivity = { ...activity, hours_spent: parseFloat(e.target.value) || 0 };
                            setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          min="0"
                          step="0.5"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          完成状态
                        </label>
                        <select
                          value={activity.completion_status}
                          onChange={(e) => {
                            const updatedActivity = { ...activity, completion_status: e.target.value };
                            setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {statusOptions.map(status => (
                            <option key={status} value={status}>{status}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        知识库链接
                      </label>
                      <input
                        type="url"
                        value={activity.knowledge_repo_link}
                        onChange={(e) => {
                          const updatedActivity = { ...activity, knowledge_repo_link: e.target.value };
                          setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        学习笔记
                      </label>
                      <textarea
                        value={activity.notes}
                        onChange={(e) => {
                          const updatedActivity = { ...activity, notes: e.target.value };
                          setActivities(activities.map(a => a.id === activity.id ? updatedActivity : a));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => setEditingActivity(null)}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
                      >
                        <X className="h-4 w-4 mr-2" />
                        取消
                      </button>
                      <button
                        onClick={() => handleUpdateActivity(activity.id!, activity)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        保存
                      </button>
                    </div>
                  </div>
                ) : (
                  // 显示模式
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        {getTypeIcon(activity.activity_type)}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                          {activity.title}
                        </h4>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {activity.activity_type}
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {activity.hours_spent}小时
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.completion_status)}`}>
                            {activity.completion_status}
                          </span>
                        </div>
                        {activity.notes && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                            {activity.notes}
                          </p>
                        )}
                        {activity.knowledge_repo_link && (
                          <a
                            href={activity.knowledge_repo_link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mt-2"
                          >
                            <Link className="h-4 w-4 mr-1" />
                            知识库链接
                          </a>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => setEditingActivity(activity.id!)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleDeleteActivity(activity.id!)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default LearningPage;
