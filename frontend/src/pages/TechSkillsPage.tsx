import { useState, useEffect } from 'react';
import MainLayout from '../components/MainLayout';
import SkillRadar<PERSON>hart from '../components/SkillRadarChart';
import { skillsAPI } from '../api';
import { Plus, Edit, Trash2, Save, X, Code, Database, Brain, Settings, TrendingUp } from 'lucide-react';

interface TechSkill {
  id?: number;
  category: string;
  name: string;
  score: number;
  evidence: string;
  priority: string;
}

const TechSkillsPage = () => {
  const [skills, setSkills] = useState<TechSkill[]>([]);
  const [isAddingSkill, setIsAddingSkill] = useState(false);
  const [editingSkill, setEditingSkill] = useState<number | null>(null);
  const [newSkill, setNewSkill] = useState<TechSkill>({
    category: '编程语言',
    name: '',
    score: 1,
    evidence: '',
    priority: 'M'
  });

  const categories = [
    { name: '编程语言', icon: Code, color: 'blue' },
    { name: '框架工具', icon: Settings, color: 'green' },
    { name: '数据处理', icon: Database, color: 'purple' },
    { name: '模型能力', icon: Brain, color: 'red' },
    { name: '工程化', icon: TrendingUp, color: 'yellow' }
  ];

  useEffect(() => {
    console.log('TechSkillsPage 组件已加载');
    loadSkills();
  }, []);

  const loadSkills = async () => {
    try {
      console.log('正在加载技能数据...');
      const response = await skillsAPI.getTechSkills();
      console.log('技能数据加载成功:', response.data);
      setSkills(response.data);
    } catch (error) {
      console.error('加载技能失败:', error);
      // 如果API调用失败，显示一些模拟数据
      setSkills([
        {
          id: 1,
          category: '编程语言',
          name: 'Python高级特性',
          score: 4,
          evidence: '完成了多个Python项目，熟练使用装饰器、生成器等高级特性',
          priority: 'H'
        }
      ]);
    }
  };

  const handleAddSkill = async () => {
    try {
      await skillsAPI.addTechSkill(newSkill);
      setIsAddingSkill(false);
      setNewSkill({ category: '编程语言', name: '', score: 1, evidence: '', priority: 'M' });
      loadSkills();
    } catch (error) {
      console.error('添加技能失败:', error);
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryInfo = categories.find(cat => cat.name === category);
    const IconComponent = categoryInfo ? categoryInfo.icon : Code;
    return <IconComponent className="h-4 w-4" />;
  };

  const getCategoryColor = (category: string) => {
    const categoryInfo = categories.find(cat => cat.name === category);
    const color = categoryInfo ? categoryInfo.color : 'blue';
    return `bg-${color}-100 text-${color}-800`;
  };

  const addDefaultSkill = (category: string, skillName: string) => {
    setNewSkill({ ...newSkill, category, name: skillName });
    setIsAddingSkill(true);
  };

  const getDefaultSkillsByCategory = (category: string) => {
    const defaultSkills = {
      '编程语言': ['Python', 'JavaScript', 'TypeScript', 'Java', 'Go', 'Rust'],
      '框架工具': ['React', 'Vue.js', 'Django', 'FastAPI', 'TensorFlow', 'PyTorch'],
      '数据处理': ['Pandas', 'NumPy', 'SQL', 'MongoDB', 'Redis', 'Elasticsearch'],
      '模型能力': ['深度学习', '自然语言处理', '计算机视觉', '推荐系统', '强化学习', 'MLOps'],
      '工程化': ['Docker', 'Kubernetes', 'CI/CD', 'Git', 'Linux', 'AWS/云服务']
    };
    return defaultSkills[category] || [];
  };

  const handleUpdateSkill = async (skillId: number, updatedSkill: TechSkill) => {
    try {
      await skillsAPI.updateTechSkill(skillId, updatedSkill);
      setEditingSkill(null);
      loadSkills();
    } catch (error) {
      console.error('更新技能失败:', error);
    }
  };

  const handleDeleteSkill = async (skillId: number) => {
    if (window.confirm('确定要删除这个技能吗？')) {
      try {
        await skillsAPI.deleteTechSkill(skillId);
        loadSkills();
      } catch (error) {
        console.error('删除技能失败:', error);
      }
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'H': return 'bg-red-100 text-red-800';
      case 'M': return 'bg-yellow-100 text-yellow-800';
      case 'L': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 4) return 'text-green-600';
    if (score >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 4) return 'bg-green-100';
    if (score >= 3) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  // 准备雷达图数据
  const radarData = skills.map(skill => ({
    subject: skill.name,
    score: skill.score,
    fullMark: 5
  }));

  const averageScore = skills.length > 0 ? skills.reduce((sum, skill) => sum + skill.score, 0) / skills.length : 0;
  const highPrioritySkills = skills.filter(skill => skill.priority === 'H').length;

  return (
    <MainLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">技术能力评估</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              评估和提升技术技能，记录证据案例和优先级
            </p>
          </div>
          <button
            onClick={() => setIsAddingSkill(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加技能
          </button>
        </div>

        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Code className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">技能总数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{skills.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">平均评分</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{averageScore.toFixed(1)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Brain className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">高优先级</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{highPrioritySkills}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <Settings className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">优秀技能</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {skills.filter(skill => skill.score >= 4).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 雷达图 */}
        {skills.length > 0 && (
          <div className="mb-6">
            <SkillRadarChart data={radarData} title="技术技能雷达图" />
          </div>
        )}

        {/* 添加技能表单 */}
        {isAddingSkill && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">添加技术技能</h3>

            {/* 快速选择常用技能 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                快速选择常用技能
              </label>
              <div className="space-y-3">
                {categories.map((category) => (
                  <div key={category.name}>
                    <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 flex items-center">
                      <category.icon className="h-4 w-4 mr-2" />
                      {category.name}
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {getDefaultSkillsByCategory(category.name).map((skillName, index) => (
                        <button
                          key={index}
                          onClick={() => addDefaultSkill(category.name, skillName)}
                          className="text-left p-2 text-sm text-blue-600 hover:bg-blue-50 rounded border border-blue-200"
                        >
                          {skillName}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">技能分类</label>
                <select
                  value={newSkill.category}
                  onChange={(e) => setNewSkill({ ...newSkill, category: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(category => (
                    <option key={category.name} value={category.name}>{category.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">技能名称</label>
                <input
                  type="text"
                  value={newSkill.name}
                  onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="例如：Python高级特性"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  评分 (1-5分)
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="range"
                    min="1"
                    max="5"
                    step="0.5"
                    value={newSkill.score}
                    onChange={(e) => setNewSkill({ ...newSkill, score: parseFloat(e.target.value) })}
                    className="flex-1"
                  />
                  <span className={`text-2xl font-bold ${getScoreColor(newSkill.score)}`}>
                    {newSkill.score}
                  </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>基础了解</span>
                  <span>熟练掌握</span>
                  <span>专家水平</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">优先级</label>
                <select
                  value={newSkill.priority}
                  onChange={(e) => setNewSkill({ ...newSkill, priority: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="H">高 (H)</option>
                  <option value="M">中 (M)</option>
                  <option value="L">低 (L)</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">证据案例</label>
              <textarea
                value={newSkill.evidence}
                onChange={(e) => setNewSkill({ ...newSkill, evidence: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="描述相关项目经验或成果..."
              />
            </div>

            <div className="flex justify-end space-x-3 mt-4">
              <button
                onClick={() => setIsAddingSkill(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </button>
              <button
                onClick={handleAddSkill}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                保存
              </button>
            </div>
          </div>
        )}

        {/* 技能列表 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">技能清单</h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {skills.map((skill) => (
              <div key={skill.id} className="p-6">
                {editingSkill === skill.id ? (
                  // 编辑表单
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">技能分类</label>
                        <select
                          value={skill.category}
                          onChange={(e) => {
                            const updatedSkill = { ...skill, category: e.target.value };
                            setSkills(skills.map(s => s.id === skill.id ? updatedSkill : s));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {categories.map(category => (
                            <option key={category.name} value={category.name}>{category.name}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">技能名称</label>
                        <input
                          type="text"
                          value={skill.name}
                          onChange={(e) => {
                            const updatedSkill = { ...skill, name: e.target.value };
                            setSkills(skills.map(s => s.id === skill.id ? updatedSkill : s));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          评分 (1-5分)
                        </label>
                        <div className="flex items-center space-x-4">
                          <input
                            type="range"
                            min="1"
                            max="5"
                            step="0.5"
                            value={skill.score}
                            onChange={(e) => {
                              const updatedSkill = { ...skill, score: parseFloat(e.target.value) };
                              setSkills(skills.map(s => s.id === skill.id ? updatedSkill : s));
                            }}
                            className="flex-1"
                          />
                          <span className={`text-2xl font-bold ${getScoreColor(skill.score)}`}>
                            {skill.score}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">优先级</label>
                        <select
                          value={skill.priority}
                          onChange={(e) => {
                            const updatedSkill = { ...skill, priority: e.target.value };
                            setSkills(skills.map(s => s.id === skill.id ? updatedSkill : s));
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="H">高 (H)</option>
                          <option value="M">中 (M)</option>
                          <option value="L">低 (L)</option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">证据案例</label>
                      <textarea
                        value={skill.evidence}
                        onChange={(e) => {
                          const updatedSkill = { ...skill, evidence: e.target.value };
                          setSkills(skills.map(s => s.id === skill.id ? updatedSkill : s));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        onClick={() => setEditingSkill(null)}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 flex items-center"
                      >
                        <X className="h-4 w-4 mr-2" />
                        取消
                      </button>
                      <button
                        onClick={() => handleUpdateSkill(skill.id!, skill)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        保存
                      </button>
                    </div>
                  </div>
                ) : (
                  // 显示模式
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex-shrink-0">
                        {getCategoryIcon(skill.category)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                            {skill.name}
                          </h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(skill.category)}`}>
                            {skill.category}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(skill.priority)}`}>
                            优先级: {skill.priority}
                          </span>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreBg(skill.score)} ${getScoreColor(skill.score)}`}>
                            {skill.score}/5
                          </span>
                        </div>
                        {skill.evidence && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <strong>证据案例：</strong>{skill.evidence}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setEditingSkill(skill.id!)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteSkill(skill.id!)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {skills.length === 0 && (
          <div className="text-center py-12">
            <Code className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无技术技能记录</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              开始添加您的技术技能评估
            </p>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default TechSkillsPage;