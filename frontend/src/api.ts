import axios from 'axios';

// 定义接口类型
interface UserData {
  username: string;
  email: string;
  password: string;
}

interface Credentials {
  username: string;
  password: string;
}

interface SkillData {
  category?: string;
  name: string;
  score: number;
  evidence?: string;
  priority?: string;
}

interface ProjectData {
  title: string;
  objective?: string;
  tech_challenge?: string;
  solution?: string;
  outcome?: string;
  reflection?: string;
  start_date?: string;
  end_date?: string;
}

interface LearningActivityData {
  activity_type: string;
  title: string;
  hours_spent?: number;
  knowledge_repo_link?: string;
  completion_status?: string;
  notes?: string;
  date?: string;
}

interface CareerGoalData {
  title: string;
  goal_type: string;
  description?: string;
  progress?: number;
  resource_gap?: string;
  target_date?: string;
}

interface IndustryKnowledgeData {
  industry: string;
  dimension: string;
  question: string;
  status?: string;
}

interface EthicsCheckData {
  check_item: string;
  status?: boolean;
  practice_case?: string;
}

interface HealthMetricData {
  metric_type: string;
  value?: number;
  description?: string;
  date?: string;
}

interface ProgressData {
  progress: number;
}

// 职业模板相关接口
interface ProfessionTemplate {
  name: string;
  display_name: string;
  description: string;
  config?: {
    tech_skill_categories: Array<{
      name: string;
      display_name: string;
      description: string;
    }>;
    soft_skills: string[];
    industries: string[];
    ethics_checks: string[];
  };
}

interface UserProfessionData {
  profession_type: string;
  profession_config?: any;
}

// 创建API实例
const API_BASE_URL = 'http://localhost:5002/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证令牌
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401错误 - 未授权
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (userData: UserData) => {
    return api.post('/auth/register', userData);
  },

  // 用户登录
  login: (credentials: Credentials) => {
    return api.post('/auth/login', credentials);
  },

  // 获取用户信息
  getUserInfo: () => {
    return api.get('/auth/user');
  },

  // 忘记密码 - 发送重置邮件
  forgotPassword: (email: string) => {
    return api.post('/auth/forgot-password', { email });
  },

  // 重置密码
  resetPassword: (token: string, newPassword: string) => {
    return api.post('/auth/reset-password', { token, new_password: newPassword });
  },
};

// 技能相关API
export const skillsAPI = {
  // 获取技术技能
  getTechSkills: () => {
    return api.get('/skills/tech-skills');
  },

  // 添加技术技能
  addTechSkill: (skillData: SkillData) => {
    return api.post('/skills/tech-skills', skillData);
  },

  // 更新技术技能
  updateTechSkill: (skillId: number, skillData: SkillData) => {
    return api.put(`/skills/tech-skills/${skillId}`, skillData);
  },

  // 删除技术技能
  deleteTechSkill: (skillId: number) => {
    return api.delete(`/skills/tech-skills/${skillId}`);
  },

  // 获取软技能
  getSoftSkills: () => {
    return api.get('/skills/soft-skills');
  },

  // 添加软技能
  addSoftSkill: (skillData: SkillData) => {
    return api.post('/skills/soft-skills', skillData);
  },

  // 更新软技能
  updateSoftSkill: (skillId: number, skillData: SkillData) => {
    return api.put(`/skills/soft-skills/${skillId}`, skillData);
  },

  // 删除软技能
  deleteSoftSkill: (skillId: number) => {
    return api.delete(`/skills/soft-skills/${skillId}`);
  },
};

// 评估相关API
export const assessmentAPI = {
  // 获取项目
  getProjects: () => {
    return api.get('/assessment/projects');
  },

  // 添加项目
  addProject: (projectData: ProjectData) => {
    return api.post('/assessment/projects', projectData);
  },

  // 更新项目
  updateProject: (projectId: number, projectData: ProjectData) => {
    return api.put(`/assessment/projects/${projectId}`, projectData);
  },

  // 删除项目
  deleteProject: (projectId: number) => {
    return api.delete(`/assessment/projects/${projectId}`);
  },

  // 获取学习活动
  getLearningActivities: () => {
    return api.get('/assessment/learning');
  },

  // 添加学习活动
  addLearningActivity: (activityData: LearningActivityData) => {
    return api.post('/assessment/learning', activityData);
  },

  // 更新学习活动
  updateLearningActivity: (activityId: number, activityData: LearningActivityData) => {
    return api.put(`/assessment/learning/${activityId}`, activityData);
  },

  // 删除学习活动
  deleteLearningActivity: (activityId: number) => {
    return api.delete(`/assessment/learning/${activityId}`);
  },

  // 获取职业目标
  getCareerGoals: () => {
    return api.get('/assessment/career-goals');
  },

  // 添加职业目标
  addCareerGoal: (goalData: CareerGoalData) => {
    return api.post('/assessment/career-goals', goalData);
  },

  // 更新职业目标进度
  updateGoalProgress: (goalId: number, progressData: ProgressData) => {
    return api.put(`/assessment/career-goals/${goalId}/progress`, progressData);
  },

  // 获取健康指标
  getHealthMetrics: () => {
    return api.get('/assessment/health-metrics');
  },

  // 添加健康指标
  addHealthMetric: (metricData: HealthMetricData) => {
    return api.post('/assessment/health-metrics', metricData);
  },

  // 获取行业知识
  getIndustryKnowledge: () => {
    return api.get('/assessment/industry-knowledge');
  },

  // 添加行业知识
  addIndustryKnowledge: (knowledgeData: IndustryKnowledgeData) => {
    return api.post('/assessment/industry-knowledge', knowledgeData);
  },

  // 更新行业知识
  updateIndustryKnowledge: (knowledgeId: number, knowledgeData: IndustryKnowledgeData) => {
    return api.put(`/assessment/industry-knowledge/${knowledgeId}`, knowledgeData);
  },

  // 删除行业知识
  deleteIndustryKnowledge: (knowledgeId: number) => {
    return api.delete(`/assessment/industry-knowledge/${knowledgeId}`);
  },

  // 获取伦理检查
  getEthicsChecks: () => {
    return api.get('/assessment/ethics-checks');
  },

  // 添加伦理检查
  addEthicsCheck: (checkData: EthicsCheckData) => {
    return api.post('/assessment/ethics-checks', checkData);
  },

  // 更新伦理检查
  updateEthicsCheck: (checkId: number, checkData: EthicsCheckData) => {
    return api.put(`/assessment/ethics-checks/${checkId}`, checkData);
  },

  // 删除伦理检查
  deleteEthicsCheck: (checkId: number) => {
    return api.delete(`/assessment/ethics-checks/${checkId}`);
  },

  // 更新健康指标
  updateHealthMetric: (metricId: number, metricData: HealthMetricData) => {
    return api.put(`/assessment/health-metrics/${metricId}`, metricData);
  },

  // 获取仪表盘数据
  getDashboardData: () => {
    return api.get('/assessment/dashboard');
  },
};

// 职业管理相关API
export const professionAPI = {
  // 获取所有职业模板
  getTemplates: () => {
    return api.get('/profession/templates');
  },

  // 获取指定职业模板详情
  getTemplate: (professionName: string) => {
    return api.get(`/profession/templates/${professionName}`);
  },

  // 获取用户职业信息
  getUserProfession: () => {
    return api.get('/profession/user-profession');
  },

  // 更新用户职业类型
  updateUserProfession: (professionData: UserProfessionData) => {
    return api.put('/profession/user-profession', professionData);
  },

  // 获取职业技能分类
  getSkillCategories: (professionName: string) => {
    return api.get(`/profession/skill-categories/${professionName}`);
  },

  // 初始化用户职业数据
  initializeUserData: (professionData: { profession_type: string }) => {
    return api.post('/profession/initialize-user-data', professionData);
  },

  // 获取仪表盘配置
  getDashboardConfig: (professionName: string) => {
    return api.get(`/profession/dashboard-config/${professionName}`);
  },
};

export default {
  auth: authAPI,
  skills: skillsAPI,
  assessment: assessmentAPI,
  profession: professionAPI,
};
