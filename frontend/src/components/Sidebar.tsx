import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3,
  Code2,
  BookOpen,
  Briefcase,
  Brain,
  Activity,
  Target,
  ShieldCheck,
  Heart,
  User,
  Settings,
  Menu,
  X,
  Briefcase as ProfessionIcon
} from 'lucide-react';
import { professionAPI } from '../api';

interface SidebarProps {
  currentPage: string;
  onPageChange: (page: string) => void;
}

const Sidebar = ({ currentPage, onPageChange }: SidebarProps) => {
  const [isOpen, setIsOpen] = useState(true);
  const [userProfession, setUserProfession] = useState<string>('AI应用工程师');

  useEffect(() => {
    loadUserProfession();
    
    // 监听职业更改事件
    const handleProfessionChange = () => {
      loadUserProfession();
    };
    
    window.addEventListener('professionChanged', handleProfessionChange);
    return () => window.removeEventListener('professionChanged', handleProfessionChange);
  }, []);

  const loadUserProfession = async () => {
    try {
      const response = await professionAPI.getUserProfession();
      const professionData = response.data;
      
      // 设置显示名称
      if (professionData.template) {
        setUserProfession(professionData.template.display_name);
      } else {
        // 如果没有模板信息，使用默认映射
        const professionMap: { [key: string]: string } = {
          'ai_engineer': 'AI应用工程师',
          'frontend_developer': '前端开发工程师',
          'backend_developer': '后端开发工程师',
          'product_manager': '产品经理',
          'data_scientist': '数据科学家',
          'ui_ux_designer': 'UI/UX设计师'
        };
        setUserProfession(professionMap[professionData.profession_type] || 'AI应用工程师');
      }
    } catch (error) {
      console.error('加载用户职业信息失败:', error);
      // 从localStorage获取用户职业信息作为备选
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          setUserProfession(user.profession || 'AI应用工程师');
        } catch {
          setUserProfession('AI应用工程师');
        }
      }
    }
  };

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const menuItems = [
    { icon: <BarChart3 size={20} />, label: '仪表盘', path: '/' },
    { icon: <Code2 size={20} />, label: '技术能力评估', path: '/tech-skills' },
    { icon: <Briefcase size={20} />, label: '项目经验复盘', path: '/projects' },
    { icon: <BookOpen size={20} />, label: '学习能力诊断', path: '/learning' },
    { icon: <Brain size={20} />, label: '行业知识匹配', path: '/industry' },
    { icon: <Activity size={20} />, label: '软技能雷达图', path: '/soft-skills' },
    { icon: <Target size={20} />, label: '职业发展对标', path: '/career' },
    { icon: <ShieldCheck size={20} />, label: '伦理与合规', path: '/ethics' },
    { icon: <Heart size={20} />, label: '健康与动力', path: '/health' },
  ];

  const bottomMenuItems = [
    { icon: <User size={20} />, label: '个人资料', path: '/profile' },
    { icon: <Settings size={20} />, label: '设置', path: '/settings' },
  ];

  const handleMenuClick = (path: string) => {
    onPageChange(path);
    // 在移动端点击后关闭侧边栏
    if (window.innerWidth < 1024) {
      setIsOpen(false);
    }
  };

  const isActivePage = (path: string) => {
    return currentPage === path;
  };

  return (
    <>
      {/* 移动端菜单按钮 */}
      <button
        className="fixed z-50 bottom-4 right-4 p-2 rounded-full bg-blue-600 text-white lg:hidden"
        onClick={toggleSidebar}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* 侧边栏 */}
      <aside
        className={`${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:w-64 lg:shrink-0`}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">SF</span>
            </div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">SelfEvo</h2>
          </div>
        </div>

        {/* 侧边栏菜单 */}
        <div className="py-4 flex flex-col h-[calc(100%-4rem)]">
          <nav className="flex-1 px-2 space-y-1">
            {menuItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleMenuClick(item.path)}
                className={`w-full flex items-center px-4 py-2 text-left rounded-md group transition-colors ${
                  isActivePage(item.path)
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className={`mr-3 transition-colors ${
                  isActivePage(item.path)
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                }`}>
                  {item.icon}
                </span>
                <span>{item.label}</span>
              </button>
            ))}
          </nav>

          {/* 职业类型显示 */}
          <div className="px-2 mb-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <ProfessionIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <div className="flex-1 min-w-0">
                  <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">当前职业</p>
                  <p className="text-sm text-blue-800 dark:text-blue-200 truncate">{userProfession}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 底部菜单 */}
          <div className="px-2 space-y-1 border-t border-gray-200 dark:border-gray-700 pt-4">
            {bottomMenuItems.map((item, index) => (
              <button
                key={index}
                onClick={() => handleMenuClick(item.path)}
                className={`w-full flex items-center px-4 py-2 text-left rounded-md group transition-colors ${
                  isActivePage(item.path)
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <span className={`mr-3 transition-colors ${
                  isActivePage(item.path)
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-500 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                }`}>
                  {item.icon}
                </span>
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
