import { useState, useEffect } from 'react';
import Dashboard from '../pages/Dashboard';
import TechSkillsPage from '../pages/TechSkillsPage';
import ProjectsPage from '../pages/ProjectsPage';
import LearningPage from '../pages/LearningPage';
import IndustryKnowledgePage from '../pages/IndustryKnowledgePage';
import SoftSkillsPage from '../pages/SoftSkillsPage';
import CareerGoalsPage from '../pages/CareerGoalsPage';
import EthicsPage from '../pages/EthicsPage';
import HealthPage from '../pages/HealthPage';
import Sidebar from './Sidebar';
import Topbar from './Topbar';
import Login from '../pages/Login';
import ProfessionSetup from './ProfessionSetup';
import Register from '../pages/Register';
import ResetPassword from '../pages/ResetPassword';
import { professionAPI } from '../api';

const App = () => {
  const [currentPage, setCurrentPage] = useState('/');
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(!!localStorage.getItem('token'));
  const [userProfession, setUserProfession] = useState('AI应用工程师');
  const [userProfessionType, setUserProfessionType] = useState('ai_engineer');

  useEffect(() => {
    // 初始化时根据当前URL设置页面
    const path = window.location.pathname;
    console.log('初始URL路径:', path);
    setCurrentPage(path);

    // 如果已登录，加载用户职业信息
    if (isAuthenticated) {
      loadUserProfession();
    }

    // 模拟加载过程
    console.log('App组件正在初始化...');
    setTimeout(() => {
      setIsLoading(false);
      console.log('App组件初始化完成');
    }, 100);

    // 监听浏览器前进后退按钮
    const handlePopState = () => {
      const newPath = window.location.pathname;
      console.log('浏览器导航到:', newPath);
      setCurrentPage(newPath);
    };

    // 监听职业更改事件
    const handleProfessionChange = () => {
      loadUserProfession();
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('professionChanged', handleProfessionChange);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('professionChanged', handleProfessionChange);
    };
  }, [isAuthenticated]);

  const loadUserProfession = async () => {
    try {
      const response = await professionAPI.getUserProfession();
      const professionData = response.data;
      setUserProfessionType(professionData.profession_type);
      
      // 设置显示名称
      if (professionData.template) {
        setUserProfession(professionData.template.display_name);
      } else {
        // 如果没有模板信息，使用默认映射
        const professionMap: { [key: string]: string } = {
          'ai_engineer': 'AI应用工程师',
          'frontend_developer': '前端开发工程师',
          'backend_developer': '后端开发工程师',
          'product_manager': '产品经理',
          'data_scientist': '数据科学家',
          'ui_ux_designer': 'UI/UX设计师'
        };
        setUserProfession(professionMap[professionData.profession_type] || 'AI应用工程师');
      }
    } catch (error) {
      console.error('加载用户职业信息失败:', error);
      // 使用默认值
      setUserProfession('AI应用工程师');
      setUserProfessionType('ai_engineer');
    }
  };

  const handlePageChange = (page: string) => {
    console.log('页面切换到:', page);
    setCurrentPage(page);
    // 更新浏览器URL
    if (window.location.pathname !== page) {
      window.history.pushState({}, '', page);
    }
    // 强制重新渲染页面
    window.dispatchEvent(new Event('popstate'));
  };

  const handleLoginSuccess = (redirectPath?: string) => {
    setIsAuthenticated(true);
    setCurrentPage(redirectPath || '/');
    window.history.pushState({}, '', redirectPath || '/');
    // 登录成功后加载用户职业信息
    loadUserProfession();
  };

  const handleProfessionSetupComplete = () => {
    // 职业设置完成后，重新加载用户职业信息
    loadUserProfession();
    handlePageChange('/');
  };

  if (isLoading) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900 items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">正在加载...</p>
        </div>
      </div>
    );
  }

  const renderPage = () => {
    console.log('当前页面:', currentPage, '认证状态:', isAuthenticated);
    
    // 未登录且不是在/login, /register, /forgot-password，强制跳转到登录页
    if (!isAuthenticated && currentPage !== '/login' && currentPage !== '/register' && currentPage !== '/forgot-password') {
      console.log('未认证，跳转到登录页');
      window.history.pushState({}, '', `/login?redirect=${currentPage}`);
      setCurrentPage('/login');
      return <Login onLoginSuccess={handleLoginSuccess} />;
    }
    try {
      switch (currentPage) {
        case '/login':
          return <Login onLoginSuccess={handleLoginSuccess} />;
        case '/register':
          return <Register />;
        case '/forgot-password':
          return <ResetPassword />;
        case '/':
          return <Dashboard />;
        case '/tech-skills':
          return <TechSkillsPage />;
        case '/projects':
          return <ProjectsPage />;
        case '/learning':
          return <LearningPage />;
        case '/industry':
          return <IndustryKnowledgePage />;
        case '/soft-skills':
          return <SoftSkillsPage />;
        case '/career':
          return <CareerGoalsPage />;
        case '/ethics':
          return <EthicsPage />;
        case '/health':
          return <HealthPage />;
        case '/profile':
          return (
            <div className="p-6">
              <div className="max-w-4xl mx-auto">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">个人资料</h1>

                {/* 基本信息 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">基本信息</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        用户名
                      </label>
                      <input
                        type="text"
                        value={JSON.parse(localStorage.getItem('user') || '{}').username || ''}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        用户ID
                      </label>
                      <input
                        type="text"
                        value={JSON.parse(localStorage.getItem('user') || '{}').user_id || ''}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        当前职业类型
                      </label>
                      <input
                        type="text"
                        value={userProfession || 'AI应用工程师'}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        注册时间
                      </label>
                      <input
                        type="text"
                        value={new Date().toLocaleDateString('zh-CN')}
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>

                {/* 账户设置 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">账户设置</h2>
                  <div className="space-y-4">
                    <button
                      onClick={() => handlePageChange('/settings')}
                      className="w-full md:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      修改职业类型
                    </button>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <p>• 更多个人资料编辑功能正在开发中</p>
                      <p>• 如需修改用户名或其他信息，请联系管理员</p>
                    </div>
                  </div>
                </div>

                {/* 使用统计 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">使用统计</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">7</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">登录天数</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">15</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">完成评估</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">3</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">设定目标</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        case '/settings':
          return (
            <div className="p-6">
              <div className="max-w-4xl mx-auto">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">系统设置</h1>

                {/* 职业类型设置 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">职业类型设置</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    更改您的职业类型以获得相应的评估框架和功能
                  </p>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        当前职业类型：<span className="font-medium">{userProfession || 'AI应用工程师'}</span>
                      </p>
                    </div>
                    <button
                      onClick={() => handlePageChange('/profession-setup')}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      更改职业类型
                    </button>
                  </div>
                </div>

                {/* 主题设置 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">外观设置</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    自定义您的界面外观和主题
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">深色模式</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">切换浅色和深色主题</p>
                      </div>
                      <button
                        onClick={() => {
                          const isDark = document.documentElement.classList.contains('dark');
                          if (isDark) {
                            document.documentElement.classList.remove('dark');
                            localStorage.setItem('darkMode', 'false');
                          } else {
                            document.documentElement.classList.add('dark');
                            localStorage.setItem('darkMode', 'true');
                          }
                        }}
                        className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          document.documentElement.classList.contains('dark') ? 'translate-x-6' : 'translate-x-1'
                        }`} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* 通知设置 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">通知设置</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    管理您的通知偏好
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">学习提醒</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">定期提醒您完成学习目标</p>
                      </div>
                      <input type="checkbox" className="rounded" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">评估提醒</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">提醒您更新技能评估</p>
                      </div>
                      <input type="checkbox" className="rounded" defaultChecked />
                    </div>
                  </div>
                </div>

                {/* 数据管理 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">数据管理</h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    管理您的个人数据
                  </p>
                  <div className="space-y-4">
                    <button className="w-full md:w-auto bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                      导出数据
                    </button>
                    <button className="w-full md:w-auto bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors ml-0 md:ml-2">
                      重置设置
                    </button>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      <p>• 导出功能将下载您的所有评估数据</p>
                      <p>• 重置将恢复所有设置到默认状态</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        case '/profession-setup':
          return (
            <ProfessionSetup
              isFirstTime={false}
              currentProfession={userProfessionType}
              onComplete={handleProfessionSetupComplete}
            />
          );
        default:
          return <Dashboard />;
      }
    } catch (error) {
      console.error('页面渲染错误:', error);
      return (
        <div className="flex flex-col flex-1 overflow-hidden">
          <div className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="text-center py-12">
              <h2 className="text-2xl font-bold text-red-600 mb-4">页面加载错误</h2>
              <p className="text-gray-600 mb-4">页面加载时出现错误，请刷新页面重试。</p>
              <button
                onClick={() => handlePageChange('/')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                返回仪表盘
              </button>
            </div>
          </div>
        </div>
      );
    }
  };

  // 如果是登录、注册或忘记密码页面，不显示侧边栏和顶栏
  const purePages = ['/login', '/register', '/forgot-password'];
  if (purePages.includes(currentPage)) {
    return renderPage();
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar currentPage={currentPage} onPageChange={handlePageChange} />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Topbar />
        <main className="flex-1 overflow-y-auto">
          {renderPage()}
        </main>
      </div>
    </div>
  );
};

export default App;

<nav className="navbar">
  <div className="navbar-brand">
    <img src="/assets/SelfEvo-logo.svg" alt="SelfEvo Logo" className="navbar-logo" />
    <span className="navbar-title">SelfEvo</span>
  </div>
  <ul className="navbar-menu">
    <li className="navbar-item"><a href="/dashboard">仪表盘</a></li>
    <li className="navbar-item"><a href="/tech-skills">技术能力评估</a></li>
    <li className="navbar-item"><a href="/project-experience">项目经验盘点</a></li>
    <li className="navbar-item"><a href="/learning-diagnosis">学习能力诊断</a></li>
  </ul>
</nav>
// 移除重复的右侧头部元素