import { ReactNode } from 'react';
import Topbar from './Topbar';

interface MainLayoutProps {
  children: ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  return (
    <div className="flex flex-col flex-1 overflow-hidden">
      {/* 顶部导航栏 */}
    
      
      {/* 内容区域 */}
      <main className="flex-1 overflow-y-auto p-4 md:p-6">
        {children}
      </main>
      
      {/* 底部状态栏 */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-3 text-center text-sm text-gray-500 dark:text-gray-400">
        <p>AI应用开发者活动管理系统 © {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
};

export default MainLayout;
