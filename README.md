<div align="center">
  <img src="assets/SelfEvo-logo.png" alt="SelfEvo Logo" width="200"/>
  <h1>SelfEvo</h1>
  <p>全方位的职业能力评估与发展平台</p>

  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/yourusername/SelfEvo)
  [![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](CONTRIBUTING.md)
</div>

## 📁 项目结构

```
SelfEvo/
├── 📁 backend/              # 后端服务
│   ├── 📁 api/              # API路由
│   ├── 📁 models/           # 数据模型
│   ├── 📁 utils/            # 工具函数
│   ├── 📁 config/           # 配置文件
│   ├── 📄 main.py           # 主应用入口
│   └── 📄 requirements.txt  # Python依赖
├── 📁 frontend/             # 前端应用
│   ├── 📁 src/              # 源代码
│   │   ├── 📁 components/   # React组件
│   │   ├── 📁 pages/        # 页面组件
│   │   ├── 📁 hooks/        # 自定义Hooks
│   │   ├── 📁 utils/        # 工具函数
│   │   └── 📁 types/        # TypeScript类型
│   ├── 📁 public/           # 静态资源
│   └── 📄 package.json      # 前端依赖
├── 📁 scripts/              # 脚本文件
│   ├── 📁 deployment/       # 部署脚本
│   └── 📁 development/      # 开发脚本
├── 📁 docs/                 # 项目文档
├── 📁 tests/                # 测试文件
├── 📁 examples/             # 示例代码
└── 📁 assets/               # 项目资源
```

## 📋 概述

SelfEvo 是一个支持多种技术职业的全方位能力评估与发展平台，帮助技术专业人员系统性地跟踪和提升自己的技术能力、项目经验和职业发展。

<div align="center">
  <img src="assets/dashboard-preview.png" alt="SelfEvo Dashboard" width="80%"/>
</div>

## ✨ 为什么选择 SelfEvo？

- **多职业支持** - 一个平台支持6种主流技术职业
- **全方位评估** - 8大核心能力维度的综合评估
- **配置驱动** - 灵活的职业模板系统，支持无限扩展
- **数据可视化** - 直观的进度展示和能力雷达图
- **开源免费** - MIT许可证，自由使用和定制

## 🎯 支持的职业类型

| 职业类型 | 图标 | 描述 |
|---------|------|------|
| **AI应用工程师** | 🤖 | 专注于AI应用开发、模型部署和智能系统构建 |
| **前端开发工程师** | 💻 | 专注于用户界面开发和用户体验优化 |
| **后端开发工程师** | ⚙️ | 专注于服务器端开发和系统架构设计 |
| **产品经理** | 📊 | 负责产品规划、设计和管理 |
| **数据科学家** | 📈 | 专注于数据分析、机器学习和商业洞察 |
| **UI/UX设计师** | 🎨 | 专注于用户界面和用户体验设计 |

## 🚀 快速开始

### 使用Docker (推荐)

```bash
# 克隆仓库
git clone https://github.com/yourusername/SelfEvo.git
cd SelfEvo

# 使用Docker Compose启动
cd scripts/deployment
docker-compose up -d
```

访问 http://localhost:80 开始使用

### 手动安装

#### 后端启动
```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 启动后端服务
python main.py
```

#### 前端启动
```bash
# 进入前端目录
cd frontend

# 安装Node.js依赖
npm install

# 启动开发服务器
npm run dev
```

#### 使用开发脚本
```bash
# 使用便捷脚本启动
./scripts/development/start.sh

# 停止服务
./scripts/development/stop.sh
```

详细的安装说明请查看 [安装文档](docs/getting-started/installation.md)

## 📚 文档

本项目文档全部整合在本README文件中。

### 项目概述
[此处添加项目简介]

### 快速开始
[此处添加安装和使用说明]

### 功能特性
[此处列出主要功能]

### 开发指南
[此处添加开发相关说明]

### 部署说明
[此处添加部署指南]

### 贡献指南
[此处添加如何贡献的说明]

### 许可证
[此处添加许可证信息]

> 注：各章节内容正在完善中，欢迎贡献！
## 🤝 贡献

我们欢迎各种形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

<div align="center">
  <strong>SelfEvo - 铸造你的职业未来</strong>
</div>