# SelfEvo 架构设计文档

## 项目概述

SelfEvo 是一个全方位的职业能力评估与发展平台，专注于帮助技术专业人员系统性地跟踪和提升自己的技术能力、项目经验和职业发展。项目采用现代化的技术栈，注重用户体验和系统的可扩展性。

## 设计原则

### 核心原则
1. **简单实用** - 优先解决用户核心需求，避免过度设计
2. **渐进增强** - 从MVP开始，逐步添加高级功能
3. **用户中心** - 以用户体验为核心，数据驱动决策
4. **开放扩展** - 设计灵活的架构，支持未来功能扩展
5. **安全可靠** - 确保数据安全和系统稳定性

### 技术原则
- **前后端分离** - 清晰的职责分工，便于独立开发和部署
- **配置驱动** - 通过配置实现多职业类型支持
- **数据可视化** - 直观展示用户能力和成长轨迹
- **响应式设计** - 支持多设备访问
- **容器化部署** - 简化部署和运维

## 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[Web浏览器]
        U2[移动设备]
    end
    
    subgraph "前端层"
        F1[React应用]
        F2[状态管理]
        F3[UI组件库]
    end
    
    subgraph "API网关层"
        G1[Nginx反向代理]
        G2[负载均衡]
        G3[SSL终端]
    end
    
    subgraph "后端服务层"
        B1[FastAPI应用]
        B2[认证服务]
        B3[业务逻辑]
        B4[数据访问层]
    end
    
    subgraph "数据层"
        D1[PostgreSQL]
        D2[Redis缓存]
        D3[文件存储]
    end
    
    subgraph "基础设施层"
        I1[Docker容器]
        I2[监控日志]
        I3[备份恢复]
    end
    
    U1 --> F1
    U2 --> F1
    F1 --> G1
    G1 --> B1
    B1 --> D1
    B1 --> D2
    B1 --> D3
    
    B1 -.-> I2
    D1 -.-> I3
```

### 技术栈选择

#### 前端技术栈
- **React 18** - 现代化的前端框架，生态丰富
- **TypeScript** - 类型安全，提高代码质量
- **Vite** - 快速的构建工具
- **Ant Design** - 企业级UI组件库
- **Zustand** - 轻量级状态管理
- **React Query** - 数据获取和缓存
- **ECharts** - 数据可视化图表库

#### 后端技术栈
- **Python 3.11+** - 简洁高效的编程语言
- **FastAPI** - 现代化的API框架，自动生成文档
- **SQLAlchemy** - 强大的ORM框架
- **Alembic** - 数据库迁移工具
- **Pydantic** - 数据验证和序列化
- **JWT** - 无状态认证
- **Pytest** - 测试框架

#### 数据存储
- **PostgreSQL** - 可靠的关系型数据库
- **Redis** - 高性能缓存和会话存储
- **MinIO/S3** - 对象存储（文件上传）

#### 部署运维
- **Docker** - 容器化部署
- **Docker Compose** - 本地开发环境
- **Nginx** - 反向代理和静态文件服务
- **Prometheus + Grafana** - 监控告警
- **ELK Stack** - 日志管理

## 核心功能模块

### 1. 用户管理模块
**功能描述**：处理用户注册、登录、个人信息管理等基础功能

**核心特性**：
- 邮箱注册/登录
- JWT无状态认证
- 个人资料管理
- 密码安全策略
- 用户权限控制

**技术实现**：
- 密码使用bcrypt加密
- JWT Token有效期管理
- 用户会话管理
- 权限基于角色控制（RBAC）

### 2. 职业模板模块
**功能描述**：支持多种技术职业类型的配置化管理

**核心特性**：
- 6种预定义职业模板
- 8大核心能力维度
- 动态表单生成
- 模板配置管理
- 自定义评估标准

**技术实现**：
- JSON Schema驱动的动态表单
- 模板继承和扩展机制
- 配置热更新
- 版本控制支持

### 3. 能力评估模块
**功能描述**：提供全方位的技能评估和跟踪功能

**核心特性**：
- 多维度能力评估
- 评分算法和权重配置
- 历史记录和趋势分析
- 能力雷达图可视化
- 自动化评估建议

**技术实现**：
- 灵活的评分算法
- 数据聚合和统计
- 实时图表更新
- 评估数据导出

### 4. 项目经验模块
**功能描述**：记录和管理用户的项目经历和技术实践

**核心特性**：
- 项目信息详细记录
- 技术栈标签管理
- 项目成果展示
- 时间线视图
- 技能关联分析

**技术实现**：
- 富文本编辑器
- 标签系统
- 文件上传管理
- 数据关联分析

### 5. 数据可视化模块
**功能描述**：直观展示用户能力发展和成长轨迹

**核心特性**：
- 能力雷达图
- 成长趋势图
- 对比分析图
- 统计报告
- 数据导出功能

**技术实现**：
- ECharts图表库
- 响应式图表设计
- 实时数据更新
- 多格式导出

### 6. 学习建议模块
**功能描述**：基于能力评估提供个性化学习建议

**核心特性**：
- 能力缺口分析
- 学习路径推荐
- 资源推荐
- 进度跟踪
- 目标设定

**技术实现**：
- 规则引擎
- 推荐算法
- 外部资源集成
- 进度追踪

## 数据模型设计

### 核心实体关系

```mermaid
erDiagram
    User ||--o{ Assessment : has
    User ||--o{ Project : owns
    User ||--o{ LearningGoal : sets
    CareerTemplate ||--o{ Assessment : uses
    CareerTemplate ||--|| SkillFramework : defines
    Assessment ||--o{ SkillScore : contains
    Project ||--o{ TechStack : uses
    
    User {
        int id PK
        string username
        string email
        string password_hash
        string full_name
        datetime created_at
        datetime updated_at
        boolean is_active
    }
    
    CareerTemplate {
        int id PK
        string name
        string description
        json config
        string icon
        datetime created_at
        boolean is_active
    }
    
    Assessment {
        int id PK
        int user_id FK
        int career_template_id FK
        json scores
        float overall_score
        datetime created_at
        datetime updated_at
    }
    
    Project {
        int id PK
        int user_id FK
        string title
        text description
        json tech_stack
        date start_date
        date end_date
        string status
        datetime created_at
    }
    
    LearningGoal {
        int id PK
        int user_id FK
        string title
        text description
        string target_skill
        date target_date
        string status
        datetime created_at
    }
```

### 数据库表设计

#### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar_url VARCHAR(255),
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(255),
    github_username VARCHAR(50),
    linkedin_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP
);
```

#### 职业模板表 (career_templates)
```sql
CREATE TABLE career_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    config JSONB NOT NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 能力评估表 (assessments)
```sql
CREATE TABLE assessments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    career_template_id INTEGER REFERENCES career_templates(id),
    scores JSONB NOT NULL,
    overall_score DECIMAL(4,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 项目经验表 (projects)
```sql
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    tech_stack JSONB,
    start_date DATE,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'completed',
    project_url VARCHAR(255),
    github_url VARCHAR(255),
    demo_url VARCHAR(255),
    achievements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 学习目标表 (learning_goals)
```sql
CREATE TABLE learning_goals (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    target_skill VARCHAR(100),
    current_level INTEGER DEFAULT 1,
    target_level INTEGER DEFAULT 5,
    target_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    progress INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
