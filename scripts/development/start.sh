#!/bin/bash

# 自演系统平台 - 启动脚本

set -e

echo "🚀 启动自演系统平台..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message "❌ $1 未安装，请先安装 $1" $RED
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        print_message "⚠️  端口 $1 已被占用" $YELLOW
        return 1
    fi
    return 0
}

# 检查必要的命令
print_message "🔍 检查系统环境..." $BLUE
check_command "python3"
check_command "node"
check_command "npm"

# 检查Python版本
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
if [[ $(echo "$PYTHON_VERSION >= 3.8" | bc -l) -eq 0 ]]; then
    print_message "❌ Python版本需要 >= 3.8，当前版本: $PYTHON_VERSION" $RED
    exit 1
fi

# 检查Node版本
NODE_VERSION=$(node -v | sed 's/v//')
if [[ $(echo "$NODE_VERSION >= 16.0" | bc -l) -eq 0 ]]; then
    print_message "❌ Node.js版本需要 >= 16.0，当前版本: $NODE_VERSION" $RED
    exit 1
fi

print_message "✅ 系统环境检查通过" $GREEN

# 检查端口
print_message "🔍 检查端口占用..." $BLUE
if ! check_port 5002; then
    print_message "请停止占用端口5002的进程，或修改配置文件中的端口设置" $YELLOW
fi

if ! check_port 5175; then
    print_message "请停止占用端口5175的进程，或修改配置文件中的端口设置" $YELLOW
fi

# 创建必要的目录
print_message "📁 创建必要的目录..." $BLUE
mkdir -p instance
mkdir -p logs
mkdir -p data

# 安装Python依赖
print_message "📦 安装Python依赖..." $BLUE
cd ../../backend
if [ ! -d "venv" ]; then
    print_message "创建Python虚拟环境..." $YELLOW
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

# 安装Node.js依赖
print_message "📦 安装Node.js依赖..." $BLUE
cd ../frontend
npm install
cd ../scripts/development

# 初始化数据库
print_message "🗄️  初始化数据库..." $BLUE
cd ../../backend
python3 -c "
from main import app, db
with app.app_context():
    db.create_all()
    print('数据库初始化完成')
"

# 启动后端服务
print_message "🚀 启动后端服务..." $BLUE
python3 main.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -f http://localhost:5002/api >/dev/null 2>&1; then
    print_message "✅ 后端服务启动成功 (PID: $BACKEND_PID)" $GREEN
else
    print_message "❌ 后端服务启动失败" $RED
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 启动前端服务
print_message "🎨 启动前端服务..." $BLUE
cd ../frontend
npm run dev &
FRONTEND_PID=$!

# 等待前端启动
sleep 5

# 检查前端是否启动成功
if curl -f http://localhost:5175 >/dev/null 2>&1; then
    print_message "✅ 前端服务启动成功 (PID: $FRONTEND_PID)" $GREEN
else
    print_message "❌ 前端服务启动失败" $RED
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

# 显示访问信息
print_message "🎉 自演系统平台启动成功！" $GREEN
echo ""
print_message "📱 访问地址:" $BLUE
print_message "   主应用: http://localhost:5175" $NC
print_message "   API服务: http://localhost:5002" $NC
print_message "   功能测试: http://localhost:5002/test_universal_features.html" $NC
print_message "   集成测试: http://localhost:5002/frontend_integration_test.html" $NC
echo ""
print_message "🔧 服务进程:" $BLUE
print_message "   后端PID: $BACKEND_PID" $NC
print_message "   前端PID: $FRONTEND_PID" $NC
echo ""
print_message "⚠️  按 Ctrl+C 停止所有服务" $YELLOW

# 保存PID到文件
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

# 等待用户中断
trap 'print_message "🛑 正在停止服务..." $YELLOW; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true; rm -f .backend.pid .frontend.pid; print_message "✅ 服务已停止" $GREEN; exit 0' INT

# 保持脚本运行
wait
