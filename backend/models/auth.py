from flask import Blueprint, jsonify, request
from models.user import db, User
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime
import os
from functools import wraps
import secrets

auth_bp = Blueprint('auth', __name__)

# JWT令牌验证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'message': '未提供令牌'}), 401

        try:
            # 移除Bearer前缀
            token = token.split(' ')[1] if token.startswith('Bearer ') else token
            payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
            current_user_id = payload['user_id']

            # 验证用户是否存在
            user = User.query.get(current_user_id)
            if not user:
                return jsonify({'message': '用户不存在'}), 404

        except jwt.ExpiredSignatureError:
            return jsonify({'message': '令牌已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'message': '无效的令牌'}), 401
        except IndexError:
            return jsonify({'message': '令牌格式错误'}), 401

        return f(current_user_id, *args, **kwargs)
    return decorated

# 用户注册
@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    # 检查用户是否已存在
    if User.query.filter_by(username=data['username']).first() or User.query.filter_by(email=data['email']).first():
        return jsonify({'message': '用户名或邮箱已存在'}), 400

    # 创建新用户
    hashed_password = generate_password_hash(data['password'])  # 使用默认的scrypt方法
    new_user = User(
        username=data['username'],
        email=data['email'],
        password_hash=hashed_password
    )

    db.session.add(new_user)
    db.session.commit()

    return jsonify({'message': '用户注册成功'}), 201

# 用户登录
@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    user = User.query.filter_by(username=data['username']).first()

    if not user or not check_password_hash(user.password_hash, data['password']):
        return jsonify({'message': '用户名或密码错误'}), 401

    # 生成JWT令牌
    token = jwt.encode({
        'user_id': user.id,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=1)
    }, os.environ.get('SECRET_KEY', 'dev_key'), algorithm='HS256')

    return jsonify({
        'message': '登录成功',
        'token': token,
        'user_id': user.id,
        'username': user.username
    }), 200

# 获取用户信息
@auth_bp.route('/user', methods=['GET'])
def get_user():
    # 从请求头获取令牌
    token = request.headers.get('Authorization')
    if not token:
        return jsonify({'message': '未提供令牌'}), 401

    try:
        # 解码令牌
        token = token.split(' ')[1]  # 移除Bearer前缀
        payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
        user_id = payload['user_id']

        # 获取用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify({'message': '用户不存在'}), 404

        return jsonify({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'created_at': user.created_at
        }), 200

    except jwt.ExpiredSignatureError:
        return jsonify({'message': '令牌已过期'}), 401
    except jwt.InvalidTokenError:
        return jsonify({'message': '无效的令牌'}), 401

def generate_reset_token(user):
    # 简单实现：用jwt生成token
    payload = {
        'user_id': user.id,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=1)
    }
    return jwt.encode(payload, os.environ.get('SECRET_KEY', 'dev_key'), algorithm='HS256')

def send_reset_email(email, token):
    # 实际项目应集成邮件服务，这里仅打印
    print(f"=== 密码重置邮件 ===")
    print(f"收件人: {email}")
    print(f"重置令牌: {token}")
    print(f"重置链接: http://localhost:5173/forgot-password?token={token}")
    print("==================")

@auth_bp.route('/forgot-password', methods=['POST'])
def forgot_password():
    data = request.get_json()
    email = data.get('email')
    
    if not email:
        return jsonify({'message': '邮箱地址不能为空'}), 400

    user = User.query.filter_by(email=email).first()
    if not user:
        return jsonify({'message': '该邮箱未注册'}), 404

    reset_token = generate_reset_token(user)
    send_reset_email(user.email, reset_token)
    return jsonify({'message': '密码重置邮件已发送'}), 200

@auth_bp.route('/reset-password', methods=['POST'])
def reset_password():
    data = request.get_json()
    token = data.get('token')
    new_password = data.get('new_password')
    
    if not token or not new_password:
        return jsonify({'message': '令牌和新密码不能为空'}), 400
    
    if len(new_password) < 6:
        return jsonify({'message': '密码长度至少6位'}), 400
    
    try:
        payload = jwt.decode(token, os.environ.get('SECRET_KEY', 'dev_key'), algorithms=['HS256'])
        user = User.query.get(payload['user_id'])
        if not user:
            return jsonify({'message': '用户不存在'}), 404
        
        user.password_hash = generate_password_hash(new_password)
        db.session.commit()
        return jsonify({'message': '密码重置成功'}), 200
        
    except jwt.ExpiredSignatureError:
        return jsonify({'message': '重置令牌已过期'}), 400
    except jwt.InvalidTokenError:
        return jsonify({'message': '无效的重置令牌'}), 400
