#!/bin/bash

# SelfEvo 项目停止脚本
# 用于停止前端和后端服务

echo "🛑 停止 SelfEvo 项目..."

# 停止后端服务
stop_backend() {
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            echo "🔧 停止后端服务 (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            echo "✅ 后端服务已停止"
        else
            echo "⚠️  后端服务进程不存在"
        fi
        rm .backend.pid
    else
        echo "⚠️  未找到后端服务PID文件"
    fi
}

# 停止前端服务
stop_frontend() {
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            echo "🎨 停止前端服务 (PID: $FRONTEND_PID)..."
            kill $FRONTEND_PID
            echo "✅ 前端服务已停止"
        else
            echo "⚠️  前端服务进程不存在"
        fi
        rm .frontend.pid
    else
        echo "⚠️  未找到前端服务PID文件"
    fi
}

# 强制停止所有相关进程
force_stop() {
    echo "🔥 强制停止所有相关进程..."
    
    # 停止Python进程（后端）
    pkill -f "python.*main.py" 2>/dev/null && echo "✅ 强制停止后端进程"
    
    # 停止Node.js进程（前端）
    pkill -f "node.*vite" 2>/dev/null && echo "✅ 强制停止前端进程"
    
    # 停止可能的npm进程
    pkill -f "npm.*dev" 2>/dev/null && echo "✅ 强制停止npm进程"
}

# 主函数
main() {
    echo "======================================"
    echo "🛑 停止 SelfEvo 服务"
    echo "======================================"
    
    # 正常停止
    stop_backend
    stop_frontend
    
    # 等待一下让进程完全停止
    sleep 2
    
    # 检查是否还有残留进程
    if pgrep -f "python.*main.py" > /dev/null || pgrep -f "node.*vite" > /dev/null; then
        echo ""
        read -p "检测到残留进程，是否强制停止? (y/n): " force_kill
        if [[ $force_kill == "y" || $force_kill == "Y" ]]; then
            force_stop
        fi
    fi
    
    echo ""
    echo "======================================"
    echo "✅ SelfEvo 服务已停止"
    echo "======================================"
}

# 运行主函数
main
